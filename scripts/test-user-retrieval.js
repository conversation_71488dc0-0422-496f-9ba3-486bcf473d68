// Script to test user <NAME_EMAIL>
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://valqgfpmtmmqfzwhjowz.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZhbHFnZnBtdG1tcWZ6d2hqb3d6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3MzQ0MjQsImV4cCI6MjA2NjMxMDQyNH0.sB254MqNtFVE2PRIgopZrBy-8fMABfnMHjGk74LaFT8';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testUserRetrieval() {
  console.log('🔍 Testing User <NAME_EMAIL>\n');
  
  try {
    // Test 1: Direct <NAME_EMAIL>
    console.log('1. Direct <NAME_EMAIL>...');
    const { data: clarkUser, error: clarkError } = await supabase
      .from('users')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    if (clarkError) {
      console.log('❌ Error querying clark user:', clarkError);
      if (clarkError.code === 'PGRST116') {
        console.log('📭 Clark user not found in database');
      }
    } else {
      console.log('✅ Found clark user:', {
        id: clarkUser.id,
        email: clarkUser.email,
        full_name: clarkUser.full_name,
        role: clarkUser.role,
        is_active: clarkUser.is_active
      });
    }

    // Test 2: Get all users to see what's in the table
    console.log('\n2. Getting all users in database...');
    const { data: allUsers, error: allUsersError } = await supabase
      .from('users')
      .select('*');

    if (allUsersError) {
      console.log('❌ Error getting all users:', allUsersError);
    } else {
      console.log(`✅ Found ${allUsers.length} users in database:`);
      allUsers.forEach(user => {
        console.log(`   - ${user.email} (${user.full_name}) - Role: ${user.role} - Active: ${user.is_active}`);
      });
    }

    // Test 3: Try case-insensitive search
    console.log('\n3. Case-insensitive search for clark...');
    const { data: clarkCaseInsensitive, error: caseError } = await supabase
      .from('users')
      .select('*')
      .ilike('email', '%clark%');

    if (caseError) {
      console.log('❌ Error in case-insensitive search:', caseError);
    } else {
      console.log(`✅ Found ${clarkCaseInsensitive.length} users matching 'clark':`);
      clarkCaseInsensitive.forEach(user => {
        console.log(`   - ${user.email} (${user.full_name})`);
      });
    }

    // Test 4: Check table structure
    console.log('\n4. Checking table structure...');
    const { data: tableInfo, error: tableError } = await supabase
      .from('users')
      .select('*')
      .limit(1);

    if (tableError) {
      console.log('❌ Error checking table structure:', tableError);
    } else if (tableInfo.length > 0) {
      console.log('✅ Table structure (first row keys):', Object.keys(tableInfo[0]));
    } else {
      console.log('📭 Table is empty');
    }

    // Test 5: Try to create clark user if not exists
    if (clarkError && clarkError.code === 'PGRST116') {
      console.log('\n5. Creating clark user...');
      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert([{
          email: '<EMAIL>',
          full_name: 'Clark Nguyen',
          badge_number: 'CLARK001',
          department: 'Fire Investigation Unit',
          role: 'officer',
          is_active: true
        }])
        .select()
        .single();

      if (createError) {
        console.log('❌ Error creating clark user:', createError);
      } else {
        console.log('✅ Created clark user:', {
          id: newUser.id,
          email: newUser.email,
          full_name: newUser.full_name
        });
      }
    }

  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

// Run the test
testUserRetrieval();
