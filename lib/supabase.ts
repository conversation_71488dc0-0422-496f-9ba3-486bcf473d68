// Supabase client configuration and database operations
import { createClient, type User as SupabaseUser, type Session } from '@supabase/supabase-js';
import type {
  DatabaseUser,
  DatabaseCase,
  DatabaseInterview,
  DatabaseTranscription,
  DatabaseStatement,
  DatabaseAudioRecording,
  DatabaseExportLog,
  User,
  Case,
  Interview,
  Transcription,
  TranscriptData,
  TranscriptionDataStructure,
  Statement,
  AudioRecording,
  ExportLog,
  CaseFilters,
  InterviewFilters,
  CreateUserRequest,
  CreateCaseRequest,
  CreateInterviewRequest,
  CreateTranscriptionRequest,
  UpdateCaseRequest,
  UpdateInterviewRequest,
  UpdateStatementRequest,
  SupabaseResponse,
} from '@/types/database';
import {
  transformDatabaseUser,
  transformDatabaseCase,
  transformDatabaseInterview,
  transformDatabaseTranscription,
  transformDatabaseStatement,
  transformDatabaseAudioRecording,
  transformDatabaseExportLog,
  transformUserToDatabase,
  transformCaseToDatabase,
  transformInterviewToDatabase,
  transformTranscriptionToDatabase,
  transformStatementToDatabase,
  transformTranscriptionDataToFrontend,
  transformTranscriptionDataToDatabase,
} from './database-transformers';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Authentication types
export interface AuthUser {
  id: string;
  email: string;
  user_metadata?: {
    full_name?: string;
    badge_number?: string;
    department?: string;
    role?: string;
  };
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignUpData {
  email: string;
  password: string;
  full_name: string;
  badge_number?: string;
  department?: string;
  role?: 'officer' | 'admin' | 'supervisor';
}

// Authentication operations
export class AuthService {
  // Sign in with email and password
  static async signIn(credentials: LoginCredentials) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: credentials.email,
      password: credentials.password,
    });

    if (error) throw error;

    // Get user profile from our users table
    if (data.user) {
      const userProfile = await UserService.getUserByEmail(data.user.email!);
      return {
        session: data.session,
        user: data.user,
        profile: userProfile,
      };
    }

    return { session: data.session, user: data.user, profile: null };
  }

  // Sign up new user
  static async signUp(signUpData: SignUpData) {
    const { data, error } = await supabase.auth.signUp({
      email: signUpData.email,
      password: signUpData.password,
      options: {
        data: {
          full_name: signUpData.full_name,
          badge_number: signUpData.badge_number,
          department: signUpData.department,
          role: signUpData.role || 'officer',
        },
      },
    });

    if (error) throw error;

    // Create user profile in our users table
    if (data.user) {
      const userProfile = await UserService.createUser({
        email: signUpData.email,
        full_name: signUpData.full_name,
        badge_number: signUpData.badge_number,
        department: signUpData.department,
        role: signUpData.role || 'officer',
      });

      return {
        session: data.session,
        user: data.user,
        profile: userProfile,
      };
    }

    return { session: data.session, user: data.user, profile: null };
  }

  // Sign out
  static async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  }

  // Get current session
  static async getSession() {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) throw error;
    return session;
  }

  // Get current user
  static async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return user;
  }

  // Get current user profile
  static async getCurrentUserProfile(): Promise<User | null> {
    const user = await this.getCurrentUser();
    if (!user?.email) return null;

    // Try to get user from our users table
    let userProfile = await UserService.getUserByEmail(user.email);

    // If user doesn't exist in our users table, create them
    if (!userProfile && user.user_metadata) {
      try {
        userProfile = await UserService.createUser({
          email: user.email,
          full_name: user.user_metadata.full_name || user.email.split('@')[0],
          badge_number: user.user_metadata.badge_number,
          department: user.user_metadata.department || 'Fire Investigation Unit',
          role: user.user_metadata.role || 'officer',
        });
        console.log('Created user profile for:', user.email);
      } catch (error) {
        console.error('Failed to create user profile:', error);
        return null;
      }
    }

    return userProfile;
  }

  // Reset password
  static async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    });
    if (error) throw error;
  }

  // Update password
  static async updatePassword(newPassword: string) {
    const { error } = await supabase.auth.updateUser({
      password: newPassword,
    });
    if (error) throw error;
  }

  // Listen to auth state changes
  static onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback);
  }

  // Check if user is authenticated
  static async isAuthenticated(): Promise<boolean> {
    const session = await this.getSession();
    return !!session;
  }

  // Check if user has specific role
  static async hasRole(role: string): Promise<boolean> {
    const profile = await this.getCurrentUserProfile();
    return profile?.role === role;
  }

  // Check if user is admin
  static async isAdmin(): Promise<boolean> {
    return await this.hasRole('admin');
  }

  // Check if user is supervisor
  static async isSupervisor(): Promise<boolean> {
    const profile = await this.getCurrentUserProfile();
    return profile?.role === 'supervisor' || profile?.role === 'admin';
  }
}

// User operations
export class UserService {
  static async getUsers(): Promise<User[]> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .order('full_name');

    if (error) throw error;
    return (data as DatabaseUser[]).map(transformDatabaseUser);
  }

  static async getUserById(id: string): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return transformDatabaseUser(data as DatabaseUser);
  }

  static async getUserByEmail(email: string): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return transformDatabaseUser(data as DatabaseUser);
  }

  static async createUser(userData: CreateUserRequest): Promise<User> {
    const { data, error } = await supabase
      .from('users')
      .insert([userData])
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseUser(data as DatabaseUser);
  }

  static async updateUser(id: string, updates: Partial<User>): Promise<User> {
    const dbUpdates = transformUserToDatabase(updates);

    const { data, error } = await supabase
      .from('users')
      .update(dbUpdates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseUser(data as DatabaseUser);
  }
}

// Case operations
export class CaseService {
  static async getCases(filters: CaseFilters = {}): Promise<Case[]> {
    let query = supabase
      .from('cases')
      .select('*')
      .order('created_at', { ascending: false });

    if (filters.status) {
      query = query.eq('status', filters.status);
    }
    if (filters.officer) {
      query = query.eq('officer', filters.officer);
    }
    if (filters.limit) {
      query = query.limit(filters.limit);
    }
    if (filters.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
    }

    const { data, error } = await query;
    if (error) throw error;
    
    return (data as DatabaseCase[]).map(transformDatabaseCase);
  }

  static async getCaseById(id: string): Promise<Case | null> {
    const { data, error } = await supabase
      .from('cases')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }

    return transformDatabaseCase(data as DatabaseCase);
  }

  static async createCase(caseData: CreateCaseRequest): Promise<Case> {
    const { data, error } = await supabase
      .from('cases')
      .insert([caseData])
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseCase(data as DatabaseCase);
  }

  static async updateCase(id: string, updates: UpdateCaseRequest): Promise<Case> {
    const { data, error } = await supabase
      .from('cases')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseCase(data as DatabaseCase);
  }

  static async deleteCase(id: string): Promise<void> {
    const { error } = await supabase
      .from('cases')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }
}

// Interview operations
export class InterviewService {
  static async getInterviews(filters: InterviewFilters = {}): Promise<Interview[]> {
    let query = supabase
      .from('interviews')
      .select('*')
      .order('created_at', { ascending: false });

    if (filters.caseId) {
      query = query.eq('case_id', filters.caseId);
    }
    if (filters.status) {
      query = query.eq('status', filters.status);
    }
    if (filters.limit) {
      query = query.limit(filters.limit);
    }
    if (filters.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
    }

    const { data, error } = await query;
    if (error) throw error;

    return (data as DatabaseInterview[]).map(transformDatabaseInterview);
  }

  static async getInterviewById(id: string): Promise<Interview | null> {
    const { data, error } = await supabase
      .from('interviews')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return transformDatabaseInterview(data as DatabaseInterview);
  }

  static async createInterview(caseId: string, interviewData: CreateInterviewRequest): Promise<Interview> {
    const dbData = {
      case_id: caseId,
      witness_name: interviewData.witness.name,
      witness_type: interviewData.witness.type,
      witness_contact: interviewData.witness.contact,
      interview_environment: interviewData.witness.environment,
      scheduled_time: interviewData.scheduled_time,
    };

    const { data, error } = await supabase
      .from('interviews')
      .insert([dbData])
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseInterview(data as DatabaseInterview);
  }

  static async updateInterview(id: string, updates: UpdateInterviewRequest): Promise<Interview> {
    const dbUpdates: Partial<DatabaseInterview> = {};
    
    if (updates.witness) {
      dbUpdates.witness_name = updates.witness.name;
      dbUpdates.witness_type = updates.witness.type;
      dbUpdates.witness_contact = updates.witness.contact;
      dbUpdates.interview_environment = updates.witness.environment;
    }
    if (updates.status) {
      dbUpdates.status = updates.status;
    }
    if (updates.end_time) {
      dbUpdates.end_time = updates.end_time;
    }

    const { data, error } = await supabase
      .from('interviews')
      .update(dbUpdates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseInterview(data as DatabaseInterview);
  }

  static async startInterview(id: string): Promise<Interview> {
    const { data, error } = await supabase
      .from('interviews')
      .update({
        status: 'in_progress',
        start_time: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseInterview(data as DatabaseInterview);
  }

  static async endInterview(id: string): Promise<Interview> {
    const interview = await this.getInterviewById(id);
    if (!interview || !interview.startTime) {
      throw new Error('Interview not found or not started');
    }

    const endTime = new Date();
    const duration = Math.floor((endTime.getTime() - interview.startTime.getTime()) / 1000);

    const { data, error } = await supabase
      .from('interviews')
      .update({
        status: 'completed',
        end_time: endTime.toISOString(),
        duration_seconds: duration,
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseInterview(data as DatabaseInterview);
  }
}

// Transcription operations
export class TranscriptionService {
  static async getTranscription(interviewId: string): Promise<Transcription | null> {
    const { data, error } = await supabase
      .from('transcriptions')
      .select('*')
      .eq('interview_id', interviewId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return transformDatabaseTranscription(data as DatabaseTranscription);
  }

  static async getTranscriptionData(interviewId: string): Promise<TranscriptData | null> {
    const transcription = await this.getTranscription(interviewId);
    if (!transcription) return null;

    return transformTranscriptionDataToFrontend(transcription.transcriptionData);
  }

  static async createTranscription(
    interviewId: string,
    transcriptData: TranscriptData,
    officerName: string,
    witnessName: string,
    language = 'en-US'
  ): Promise<Transcription> {
    const transcriptionData = transformTranscriptionDataToDatabase(
      transcriptData,
      officerName,
      witnessName
    );

    const { data, error } = await supabase
      .from('transcriptions')
      .insert([{
        interview_id: interviewId,
        transcription_data: transcriptionData,
        language,
        confidence_score: transcriptData.metadata?.averageConfidence,
        processing_status: 'completed',
      }])
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseTranscription(data as DatabaseTranscription);
  }

  static async updateTranscription(
    interviewId: string,
    transcriptData: TranscriptData,
    officerName: string,
    witnessName: string
  ): Promise<Transcription> {
    const transcriptionData = transformTranscriptionDataToDatabase(
      transcriptData,
      officerName,
      witnessName
    );

    const { data, error } = await supabase
      .from('transcriptions')
      .update({
        transcription_data: transcriptionData,
        confidence_score: transcriptData.metadata?.averageConfidence,
        processing_status: 'completed',
      })
      .eq('interview_id', interviewId)
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseTranscription(data as DatabaseTranscription);
  }

  static async updateTranscriptionStatus(
    interviewId: string,
    status: 'pending' | 'processing' | 'completed' | 'failed'
  ): Promise<void> {
    const { error } = await supabase
      .from('transcriptions')
      .update({ processing_status: status })
      .eq('interview_id', interviewId);

    if (error) throw error;
  }

  static async addTranscriptionSegment(
    interviewId: string,
    segment: { speaker: string; timestamp: string; text: string; confidence?: number }
  ): Promise<void> {
    // Get current transcription
    const transcription = await this.getTranscription(interviewId);
    if (!transcription) {
      throw new Error('Transcription not found');
    }

    // Add new segment to existing data
    const updatedData = {
      ...transcription.transcriptionData,
      segments: [...transcription.transcriptionData.segments, segment],
    };

    // Update the transcription
    const { error } = await supabase
      .from('transcriptions')
      .update({ transcription_data: updatedData })
      .eq('interview_id', interviewId);

    if (error) throw error;
  }
}

// Statement operations
export class StatementService {
  static async getStatement(interviewId: string): Promise<Statement | null> {
    const { data, error } = await supabase
      .from('statements')
      .select('*')
      .eq('interview_id', interviewId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return transformDatabaseStatement(data as DatabaseStatement);
  }

  static async createStatement(interviewId: string, content: string, officerNotes?: string): Promise<Statement> {
    const { data, error } = await supabase
      .from('statements')
      .insert([{
        interview_id: interviewId,
        content,
        officer_notes: officerNotes,
      }])
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseStatement(data as DatabaseStatement);
  }

  static async updateStatement(interviewId: string, updates: UpdateStatementRequest): Promise<Statement> {
    const { data, error } = await supabase
      .from('statements')
      .update({
        content: updates.content,
        officer_notes: updates.officer_notes,
      })
      .eq('interview_id', interviewId)
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseStatement(data as DatabaseStatement);
  }
}

// Audio recording operations
export class AudioRecordingService {
  static async getAudioRecording(interviewId: string): Promise<AudioRecording | null> {
    const { data, error } = await supabase
      .from('audio_recordings')
      .select('*')
      .eq('interview_id', interviewId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return transformDatabaseAudioRecording(data as DatabaseAudioRecording);
  }

  static async createAudioRecording(
    interviewId: string,
    filePath: string,
    metadata: {
      fileSize?: number;
      duration?: number;
      format?: string;
      sampleRate?: number;
      channels?: number;
    }
  ): Promise<AudioRecording> {
    const { data, error } = await supabase
      .from('audio_recordings')
      .insert([{
        interview_id: interviewId,
        file_path: filePath,
        file_size: metadata.fileSize,
        duration_seconds: metadata.duration,
        format: metadata.format,
        sample_rate: metadata.sampleRate,
        channels: metadata.channels,
      }])
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseAudioRecording(data as DatabaseAudioRecording);
  }
}

// Export log operations
export class ExportLogService {
  static async getExportLogs(interviewId: string): Promise<ExportLog[]> {
    const { data, error } = await supabase
      .from('export_logs')
      .select('*')
      .eq('interview_id', interviewId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return (data as DatabaseExportLog[]).map(transformDatabaseExportLog);
  }

  static async logExport(
    interviewId: string,
    exportType: 'pdf' | 'docx',
    filePath?: string,
    exportedByUserId?: string
  ): Promise<ExportLog> {
    const { data, error } = await supabase
      .from('export_logs')
      .insert([{
        interview_id: interviewId,
        export_type: exportType,
        file_path: filePath,
        exported_by_user_id: exportedByUserId,
      }])
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseExportLog(data as DatabaseExportLog);
  }
}

// Storage operations
export class StorageService {
  static async uploadAudioFile(interviewId: string, file: File): Promise<string> {
    const fileName = `${interviewId}/${Date.now()}-${file.name}`;
    
    const { data, error } = await supabase.storage
      .from('audio-recordings')
      .upload(fileName, file);

    if (error) throw error;
    return data.path;
  }

  static async getAudioFileUrl(filePath: string): Promise<string> {
    const { data } = supabase.storage
      .from('audio-recordings')
      .getPublicUrl(filePath);

    return data.publicUrl;
  }

  static async uploadExportFile(interviewId: string, file: Blob, fileName: string): Promise<string> {
    const filePath = `${interviewId}/${Date.now()}-${fileName}`;
    
    const { data, error } = await supabase.storage
      .from('exported-documents')
      .upload(filePath, file);

    if (error) throw error;
    return data.path;
  }

  static async getExportFileUrl(filePath: string): Promise<string> {
    const { data } = supabase.storage
      .from('exported-documents')
      .getPublicUrl(filePath);

    return data.publicUrl;
  }
}

// Real-time subscriptions
export class RealtimeService {
  static subscribeToInterview(interviewId: string, callback: (interview: Interview) => void) {
    return supabase
      .channel(`interview-${interviewId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'interviews',
          filter: `id=eq.${interviewId}`,
        },
        (payload) => {
          const interview = transformDatabaseInterview(payload.new as DatabaseInterview);
          callback(interview);
        }
      )
      .subscribe();
  }

  static subscribeToTranscription(interviewId: string, callback: (transcription: Transcription) => void) {
    return supabase
      .channel(`transcription-${interviewId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'transcriptions',
          filter: `interview_id=eq.${interviewId}`,
        },
        (payload: any) => {
          const transcription = transformDatabaseTranscription(payload.new as DatabaseTranscription);
          callback(transcription);
        }
      )
      .subscribe();
  }
}
