import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(req: NextRequest) {
  const res = NextResponse.next();
  const supabase = createMiddlewareClient({ req, res });

  // Refresh session if expired - required for Server Components
  const {
    data: { session },
  } = await supabase.auth.getSession();

  // Protected routes that require authentication
  const protectedRoutes = [
    '/dashboard',
    '/cases',
    '/interviews',
    '/api/cases',
    '/api/interviews',
    '/api/transcriptions',
    '/api/statements',
    '/api/audio',
    '/api/export',
  ];

  // Auth routes that should redirect if already authenticated
  const authRoutes = ['/login', '/signup', '/auth'];

  const isProtectedRoute = protectedRoutes.some(route => 
    req.nextUrl.pathname.startsWith(route)
  );
  
  const isAuthRoute = authRoutes.some(route => 
    req.nextUrl.pathname.startsWith(route)
  );

  // If accessing protected route without session, redirect to login
  if (isProtectedRoute && !session) {
    const redirectUrl = new URL('/login', req.url);
    redirectUrl.searchParams.set('redirectTo', req.nextUrl.pathname);
    return NextResponse.redirect(redirectUrl);
  }

  // If accessing auth route with session, redirect to dashboard
  if (isAuthRoute && session) {
    return NextResponse.redirect(new URL('/dashboard', req.url));
  }

  // For API routes, check authentication and authorization
  if (req.nextUrl.pathname.startsWith('/api/')) {
    // Skip auth check for public API routes
    const publicApiRoutes = ['/api/auth', '/api/health'];
    const isPublicApiRoute = publicApiRoutes.some(route =>
      req.nextUrl.pathname.startsWith(route)
    );

    // Allow POST to test endpoint (public test)
    const isPublicTestRoute = req.nextUrl.pathname === '/api/test' && req.method === 'POST';

    if (!isPublicApiRoute && !isPublicTestRoute && !session) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      );
    }

    // Add user info to headers for API routes
    if (session?.user) {
      const requestHeaders = new Headers(req.headers);
      requestHeaders.set('x-user-id', session.user.id);
      requestHeaders.set('x-user-email', session.user.email || '');
      
      // Get user role from database
      try {
        const { data: userProfile } = await supabase
          .from('users')
          .select('role, department')
          .eq('email', session.user.email)
          .single();

        if (userProfile) {
          requestHeaders.set('x-user-role', userProfile.role);
          requestHeaders.set('x-user-department', userProfile.department || '');
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
      }

      return NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      });
    }
  }

  return res;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
