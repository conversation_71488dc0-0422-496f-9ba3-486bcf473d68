"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft } from "lucide-react"
import type { Screen, AppState } from "@/app/page"
import type { Case } from "@/types/database"

interface CaseSelectionScreenProps {
  onNavigate: (screen: Screen) => void
  appState: AppState
  updateAppState: (updates: Partial<AppState>) => void
}

const mockCases: Case[] = [
  {
    id: "FIU-2025-001",
    incidentLocation: "123 Main Street, Apartment 4B",
    incidentDate: "2025-06-20",
    incidentTime: "14:30",
    assignedOfficerId: "550e8400-e29b-41d4-a716-446655440001",
    assignedOfficer: {
      id: "550e8400-e29b-41d4-a716-446655440001",
      email: "<EMAIL>",
      fullName: "Detective <PERSON>",
      role: "officer",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    status: "In Progress",
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: "FIU-2025-002",
    incidentLocation: "456 Oak Avenue, Single Family Home",
    incidentDate: "2025-06-18",
    incidentTime: "09:15",
    assignedOfficerId: "550e8400-e29b-41d4-a716-446655440002",
    assignedOfficer: {
      id: "550e8400-e29b-41d4-a716-446655440002",
      email: "<EMAIL>",
      fullName: "Detective Smith",
      role: "officer",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    status: "Completed",
    createdAt: new Date(),
    updatedAt: new Date(),
  },
]

export function CaseSelectionScreen({ onNavigate, appState, updateAppState }: CaseSelectionScreenProps) {
  const selectCase = (selectedCase: Case) => {
    updateAppState({ currentCase: selectedCase })
    onNavigate("witness-setup")
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(":")
    const hour = Number.parseInt(hours)
    const ampm = hour >= 12 ? "PM" : "AM"
    const displayHour = hour % 12 || 12
    return `${displayHour}:${minutes} ${ampm}`
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8 pb-4 border-b">
        <Button variant="outline" size="sm" onClick={() => onNavigate("start-screen")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <h2 className="text-2xl font-semibold">Select Case</h2>
        <div></div>
      </div>

      <div className="space-y-4 mb-6">
        {mockCases.map((caseItem) => (
          <Card
            key={caseItem.id}
            className="cursor-pointer hover:shadow-md transition-all duration-200 hover:-translate-y-1"
            onClick={() => selectCase(caseItem)}
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-semibold text-primary">{caseItem.id}</h4>
                <Badge variant={caseItem.status === "In Progress" ? "secondary" : "default"}>{caseItem.status}</Badge>
              </div>
              <div className="space-y-1 text-sm">
                <p>
                  <strong>Location:</strong> {caseItem.incidentLocation}
                </p>
                <p>
                  <strong>Date:</strong> {formatDate(caseItem.incidentDate)} at {formatTime(caseItem.incidentTime)}
                </p>
                <p>
                  <strong>Officer:</strong> {caseItem.assignedOfficer?.fullName || 'Unknown'}
                </p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Button variant="secondary" className="w-full" onClick={() => onNavigate("new-case")}>
        Create New Case
      </Button>
    </div>
  )
}
