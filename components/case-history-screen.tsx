"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft } from "lucide-react"
import type { Screen } from "@/app/page"

interface CaseHistoryScreenProps {
  onNavigate: (screen: Screen) => void
}

const mockCaseHistory = [
  {
    id: "FIU-2025-001",
    location: "123 Main Street, Apartment 4B",
    date: "June 20, 2025",
    interviews: 1,
    status: "In Progress" as const,
  },
  {
    id: "FIU-2025-002",
    location: "456 Oak Avenue",
    date: "June 18, 2025",
    interviews: 3,
    status: "Completed" as const,
  },
]

export function CaseHistoryScreen({ onNavigate }: CaseHistoryScreenProps) {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8 pb-4 border-b">
        <Button variant="outline" size="sm" onClick={() => onNavigate("start-screen")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <h2 className="text-2xl font-semibold">Case History</h2>
        <div></div>
      </div>

      <div className="space-y-4">
        {mockCaseHistory.map((caseItem) => (
          <Card key={caseItem.id}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-semibold text-primary">{caseItem.id}</h4>
                <Badge variant={caseItem.status === "In Progress" ? "secondary" : "default"}>{caseItem.status}</Badge>
              </div>
              <div className="space-y-1 text-sm">
                <p>
                  <strong>Location:</strong> {caseItem.location}
                </p>
                <p>
                  <strong>Date:</strong> {caseItem.date}
                </p>
                <p>
                  <strong>Interviews:</strong> {caseItem.interviews} completed
                </p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
