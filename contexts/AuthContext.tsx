'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { AuthService } from '@/lib/supabase';
import type { User } from '@/types/database';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (signUpData: {
    email: string;
    password: string;
    full_name: string;
    badge_number?: string;
    department?: string;
    role?: 'officer' | 'admin' | 'supervisor';
  }) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updatePassword: (newPassword: string) => Promise<void>;
  isAuthenticated: boolean;
  isAdmin: boolean;
  isSupervisor: boolean;
  hasRole: (role: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const profile = await AuthService.getCurrentUserProfile();
        setUser(profile);
      } catch (error) {
        console.error('Error getting initial session:', error);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = AuthService.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          try {
            const profile = await AuthService.getCurrentUserProfile();
            setUser(profile);
          } catch (error) {
            console.error('Error getting user profile:', error);
            setUser(null);
          }
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
        }
        setLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    setLoading(true);
    try {
      const result = await AuthService.signIn({ email, password });
      setUser(result.profile);
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  const signUp = async (signUpData: {
    email: string;
    password: string;
    full_name: string;
    badge_number?: string;
    department?: string;
    role?: 'officer' | 'admin' | 'supervisor';
  }) => {
    setLoading(true);
    try {
      const result = await AuthService.signUp(signUpData);
      setUser(result.profile);
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  const signOut = async () => {
    setLoading(true);
    try {
      await AuthService.signOut();
      setUser(null);
    } catch (error) {
      console.error('Error signing out:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    await AuthService.resetPassword(email);
  };

  const updatePassword = async (newPassword: string) => {
    await AuthService.updatePassword(newPassword);
  };

  const isAuthenticated = !!user;
  const isAdmin = user?.role === 'admin';
  const isSupervisor = user?.role === 'supervisor' || user?.role === 'admin';
  
  const hasRole = (role: string) => user?.role === role;

  const value: AuthContextType = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    isAuthenticated,
    isAdmin,
    isSupervisor,
    hasRole,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Higher-order component for protecting routes
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  requiredRole?: string
) {
  return function AuthenticatedComponent(props: P) {
    const { user, loading, hasRole } = useAuth();

    if (loading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
        </div>
      );
    }

    if (!user) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
            <p className="text-gray-600">Please sign in to access this page.</p>
          </div>
        </div>
      );
    }

    if (requiredRole && !hasRole(requiredRole)) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Insufficient Permissions</h1>
            <p className="text-gray-600">You don't have permission to access this page.</p>
          </div>
        </div>
      );
    }

    return <Component {...props} />;
  };
}

// Hook for role-based access control
export function usePermissions() {
  const { user, isAdmin, isSupervisor, hasRole } = useAuth();

  const canViewAllCases = isAdmin || isSupervisor;
  const canCreateCases = isAdmin || hasRole('officer');
  const canEditCases = isAdmin;
  const canDeleteCases = isAdmin;
  
  const canViewAllInterviews = isAdmin || isSupervisor;
  const canCreateInterviews = isAdmin || hasRole('officer');
  const canEditInterviews = isAdmin;
  const canDeleteInterviews = isAdmin;
  
  const canManageUsers = isAdmin;
  const canViewReports = isAdmin || isSupervisor;
  const canExportData = isAdmin || isSupervisor || hasRole('officer');

  return {
    user,
    canViewAllCases,
    canCreateCases,
    canEditCases,
    canDeleteCases,
    canViewAllInterviews,
    canCreateInterviews,
    canEditInterviews,
    canDeleteInterviews,
    canManageUsers,
    canViewReports,
    canExportData,
  };
}
